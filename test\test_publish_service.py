"""
Unit tests for PublishService.

This module contains tests for the PublishService class functionality
including project publishing and series publishing operations.
"""

import unittest
from unittest.mock import Mock, MagicMock, patch
import sys
import os
import tempfile
import shutil
import datetime

# Add the parent directory to the path to import from apps
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from apps.publish.services.publish_service import (
    PublishService, PublishConfig, PublishResult, PublishError
)
from apps.publish.integrations.e3_client import E3ConnectionError, E3OperationError
from apps.publish.integrations.report_generator import ReportGeneratorError
from apps.publish.services.export_service import ExportError, ExportResult
from apps.publish.services.manual_service import ManualError, ManualResult


class MockProjectData:
    """Mock project data for testing."""
    
    def __init__(self, gss_parent="TEST123", serial_number="001", model="P408"):
        self.gss_parent = gss_parent
        self.serial_number = serial_number
        self.model = model
        self.customer = "Test Customer"
        self.location = "Test Location"
        self.title = "Test Title"
        self.sales_order = "SO123"


class TestPublishService(unittest.TestCase):
    """Test cases for PublishService class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_e3_client = Mock()
        self.mock_export_service = Mock()
        self.mock_manual_service = Mock()
        self.mock_report_generator = Mock()
        
        self.service = PublishService(
            e3_client=self.mock_e3_client,
            export_service=self.mock_export_service,
            manual_service=self.mock_manual_service,
            report_generator=self.mock_report_generator
        )
        
        self.project_data = MockProjectData()
        self.publish_config = PublishConfig()
        
        # Create temporary directory for test outputs
        self.temp_dir = tempfile.mkdtemp()
        self.publish_config.output_base_path = self.temp_dir
        
    def tearDown(self):
        """Clean up test fixtures."""
        # Remove temporary directory
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_init_with_all_services(self):
        """Test PublishService initialization with all services."""
        service = PublishService(
            e3_client=self.mock_e3_client,
            export_service=self.mock_export_service,
            manual_service=self.mock_manual_service,
            report_generator=self.mock_report_generator
        )
        
        self.assertEqual(service.e3_client, self.mock_e3_client)
        self.assertEqual(service.export_service, self.mock_export_service)
        self.assertEqual(service.manual_service, self.mock_manual_service)
        self.assertEqual(service.report_generator, self.mock_report_generator)
    
    def test_init_with_minimal_services(self):
        """Test PublishService initialization with minimal services."""
        service = PublishService()
        
        self.assertIsNone(service.e3_client)
        self.assertIsNone(service.export_service)
        self.assertIsNone(service.manual_service)
        self.assertIsNone(service.report_generator)
    
    def test_publish_project_success_full_workflow(self):
        """Test successful project publishing with full workflow."""
        # Setup mocks
        self.mock_e3_client.get_process_id.return_value = 12345
        self.mock_report_generator.update_bom_reports.return_value = {"PHOENIX BOM": True, "Simplified BOM": True}
        
        export_result = ExportResult()
        export_result.success = True
        export_result.pdf_files = ["test.pdf"]
        export_result.dxf_files = ["test.dxf"]
        export_result.errors = []
        export_result.warnings = []
        self.mock_export_service.export_project.return_value = export_result
        
        manual_result = ManualResult()
        manual_result.success = True
        manual_result.created_files = ["manual.pdf"]
        manual_result.errors = []
        manual_result.warnings = []
        self.mock_manual_service.can_create_manual_for_model.return_value = True
        self.mock_manual_service.create_manual.return_value = manual_result
        
        self.mock_report_generator.export_gss_bom.return_value = "gss_bom.xlsx"
        
        # Configure publish config
        self.publish_config.create_manual = True
        
        # Execute
        result = self.service.publish_project(self.project_data, self.publish_config)
        
        # Verify
        self.assertTrue(result.success)
        self.assertEqual(result.serial_number, "001")
        self.assertTrue(result.output_path.endswith("TEST123 001"))
        self.assertEqual(len(result.errors), 0)
        self.assertIn("update_attributes", result.steps_completed)
        self.assertIn("update_bom_reports", result.steps_completed)
        self.assertIn("export_files", result.steps_completed)
        self.assertIn("create_manual", result.steps_completed)
        self.assertIn("export_gss_bom", result.steps_completed)
        self.assertIn("save_job_data", result.steps_completed)
    
    def test_publish_project_validation_error(self):
        """Test project publishing with validation error."""
        invalid_data = MockProjectData()
        invalid_data.gss_parent = None
        
        with self.assertRaises(PublishError) as context:
            self.service.publish_project(invalid_data, self.publish_config)
        
        self.assertIn("GSS parent number is required", str(context.exception))
    
    def test_publish_project_no_project_data(self):
        """Test project publishing without project data."""
        with self.assertRaises(PublishError) as context:
            self.service.publish_project(None, self.publish_config)
        
        self.assertIn("Project data is required", str(context.exception))
    
    def test_publish_project_no_config(self):
        """Test project publishing without configuration."""
        with self.assertRaises(PublishError) as context:
            self.service.publish_project(self.project_data, None)
        
        self.assertIn("Publish configuration is required", str(context.exception))
    
    def test_publish_project_e3_error_continue(self):
        """Test project publishing with E3 error but continue processing."""
        # Disable services that might cause issues
        self.publish_config.update_bom_reports = False
        self.publish_config.export_gss_bom = False
        
        self.mock_e3_client.update_attributes.side_effect = E3ConnectionError("Connection failed")
        self.publish_config.fail_on_e3_errors = False
        
        # Setup other mocks to succeed
        export_result = ExportResult()
        export_result.success = True
        export_result.errors = []
        export_result.warnings = []
        export_result.pdf_files = []
        export_result.dxf_files = []
        self.mock_export_service.export_project.return_value = export_result
        
        result = self.service.publish_project(self.project_data, self.publish_config)
        
        self.assertFalse(result.success)  # Should fail due to E3 error
        self.assertEqual(len(result.errors), 1)
        self.assertIn("Failed to update E3 attributes", result.errors[0])
    
    def test_publish_project_e3_error_fail_fast(self):
        """Test project publishing with E3 error and fail fast."""
        self.mock_e3_client.update_attributes.side_effect = E3ConnectionError("Connection failed")
        self.publish_config.fail_on_e3_errors = True
        
        with self.assertRaises(PublishError) as context:
            self.service.publish_project(self.project_data, self.publish_config)
        
        self.assertIn("Failed to update E3 attributes", str(context.exception))
    
    def test_publish_project_export_error(self):
        """Test project publishing with export error."""
        # Disable other services that might cause issues
        self.publish_config.update_bom_reports = False
        self.publish_config.export_gss_bom = False
        
        self.mock_export_service.export_project.side_effect = ExportError("Export failed")
        self.publish_config.fail_on_export_errors = False
        
        result = self.service.publish_project(self.project_data, self.publish_config)
        
        self.assertFalse(result.success)
        self.assertEqual(len(result.errors), 1)
        self.assertIn("Failed to export project", result.errors[0])
    
    def test_publish_project_manual_error(self):
        """Test project publishing with manual creation error."""
        # Disable other services that might cause issues
        self.publish_config.update_bom_reports = False
        self.publish_config.export_gss_bom = False
        
        # Setup export service to succeed
        export_result = ExportResult()
        export_result.success = True
        export_result.errors = []
        export_result.warnings = []
        export_result.pdf_files = []
        export_result.dxf_files = []
        self.mock_export_service.export_project.return_value = export_result
        
        self.mock_manual_service.can_create_manual_for_model.return_value = True
        self.mock_manual_service.create_manual.side_effect = ManualError("Manual failed")
        self.publish_config.create_manual = True
        self.publish_config.fail_on_manual_errors = False
        
        result = self.service.publish_project(self.project_data, self.publish_config)
        
        self.assertFalse(result.success)
        self.assertEqual(len(result.errors), 1)
        self.assertIn("Failed to create manual", result.errors[0])
    
    def test_publish_project_report_error(self):
        """Test project publishing with report generation error."""
        # Disable other services that might cause issues
        self.publish_config.export_gss_bom = False
        
        # Setup export service to succeed
        export_result = ExportResult()
        export_result.success = True
        export_result.errors = []
        export_result.warnings = []
        export_result.pdf_files = []
        export_result.dxf_files = []
        self.mock_export_service.export_project.return_value = export_result
        
        self.mock_e3_client.get_process_id.return_value = 12345
        self.mock_report_generator.update_bom_reports.side_effect = ReportGeneratorError("Report failed")
        self.publish_config.fail_on_report_errors = False
        
        result = self.service.publish_project(self.project_data, self.publish_config)
        
        self.assertFalse(result.success)
        self.assertEqual(len(result.errors), 1)
        self.assertIn("Failed to update BOM reports", result.errors[0])
    
    def test_publish_project_no_services(self):
        """Test project publishing with no services available."""
        service = PublishService()  # No services
        
        result = service.publish_project(self.project_data, self.publish_config)
        
        self.assertTrue(result.success)  # Should succeed with warnings
        self.assertGreater(len(result.warnings), 0)
        self.assertIn("E3 client not available", result.warnings[0])
    
    def test_publish_project_manual_not_supported(self):
        """Test project publishing when manual is not supported for model."""
        # Disable other services that might cause issues
        self.publish_config.update_bom_reports = False
        self.publish_config.export_gss_bom = False
        
        # Setup export service to succeed
        export_result = ExportResult()
        export_result.success = True
        export_result.errors = []
        export_result.warnings = []
        export_result.pdf_files = []
        export_result.dxf_files = []
        self.mock_export_service.export_project.return_value = export_result
        
        self.mock_manual_service.can_create_manual_for_model.return_value = False
        self.publish_config.create_manual = True
        
        result = self.service.publish_project(self.project_data, self.publish_config)
        
        self.assertTrue(result.success)
        self.assertEqual(len(result.warnings), 1)
        self.assertIn("Manual creation not supported", result.warnings[0])
    
    def test_publish_series_success(self):
        """Test successful series publishing."""
        # Disable services that might cause issues
        self.publish_config.update_bom_reports = False
        self.publish_config.export_gss_bom = False
        self.publish_config.create_manual = False
        
        # Setup mocks for successful publishing
        export_result = ExportResult()
        export_result.success = True
        export_result.errors = []
        export_result.warnings = []
        export_result.pdf_files = []
        export_result.dxf_files = []
        self.mock_export_service.export_project.return_value = export_result
        
        results = self.service.publish_series(self.project_data, self.publish_config, 3)
        
        self.assertEqual(len(results), 3)
        for i, result in enumerate(results):
            self.assertTrue(result.success)
            expected_serial = str(int(self.project_data.serial_number) + i).zfill(3)
            self.assertEqual(result.serial_number, expected_serial)
    
    def test_publish_series_invalid_count(self):
        """Test series publishing with invalid count."""
        with self.assertRaises(PublishError) as context:
            self.service.publish_series(self.project_data, self.publish_config, 0)
        
        self.assertIn("Series count must be greater than 0", str(context.exception))
    
    def test_publish_series_with_errors_continue(self):
        """Test series publishing with errors but continue processing."""
        # Disable services that might cause issues
        self.publish_config.update_bom_reports = False
        self.publish_config.export_gss_bom = False
        self.publish_config.create_manual = False
        
        # First project succeeds, second fails, third succeeds
        export_results = [
            ExportResult(),  # Success (default)
            None,  # Will cause error
            ExportResult()   # Success
        ]
        export_results[0].success = True
        export_results[0].errors = []
        export_results[0].warnings = []
        export_results[0].pdf_files = []
        export_results[0].dxf_files = []
        
        export_results[2].success = True
        export_results[2].errors = []
        export_results[2].warnings = []
        export_results[2].pdf_files = []
        export_results[2].dxf_files = []
        
        def side_effect(*args, **kwargs):
            result = export_results.pop(0)
            if result is None:
                raise ExportError("Export failed")
            return result
        
        self.mock_export_service.export_project.side_effect = side_effect
        self.publish_config.stop_series_on_error = False
        
        results = self.service.publish_series(self.project_data, self.publish_config, 3)
        
        self.assertEqual(len(results), 3)
        self.assertTrue(results[0].success)
        self.assertFalse(results[1].success)
        self.assertTrue(results[2].success)
    
    def test_publish_series_stop_on_error(self):
        """Test series publishing that stops on first error."""
        # Disable services that might cause issues
        self.publish_config.update_bom_reports = False
        self.publish_config.export_gss_bom = False
        self.publish_config.create_manual = False
        
        # First project succeeds, second fails
        export_results = [ExportResult(), None]
        export_results[0].success = True
        export_results[0].errors = []
        export_results[0].warnings = []
        export_results[0].pdf_files = []
        export_results[0].dxf_files = []
        
        def side_effect(*args, **kwargs):
            result = export_results.pop(0)
            if result is None:
                raise ExportError("Export failed")
            return result
        
        self.mock_export_service.export_project.side_effect = side_effect
        self.publish_config.stop_series_on_error = True
        
        results = self.service.publish_series(self.project_data, self.publish_config, 3)
        
        self.assertEqual(len(results), 2)  # Should stop after second failure
        self.assertTrue(results[0].success)
        self.assertFalse(results[1].success)
    
    def test_increment_serial_number_numeric(self):
        """Test serial number increment for numeric serials."""
        result = self.service._increment_serial_number("001", 1)
        self.assertEqual(result, "002")
        
        result = self.service._increment_serial_number("099", 1)
        self.assertEqual(result, "100")
    
    def test_increment_serial_number_alphanumeric(self):
        """Test serial number increment for alphanumeric serials."""
        result = self.service._increment_serial_number("ABC123", 1)
        self.assertEqual(result, "ABC123+1")
        
        result = self.service._increment_serial_number("TEST", 5)
        self.assertEqual(result, "TEST+5")
    
    def test_create_output_folder(self):
        """Test output folder creation."""
        folder_path = self.service._create_output_folder(self.project_data, self.temp_dir)
        
        expected_path = os.path.join(self.temp_dir, "TEST123 001")
        self.assertEqual(folder_path, expected_path)
        self.assertTrue(os.path.exists(folder_path))
    
    def test_create_output_folder_no_base_path(self):
        """Test output folder creation without base path."""
        with self.assertRaises(PublishError) as context:
            self.service._create_output_folder(self.project_data, "")
        
        self.assertIn("Output base path is required", str(context.exception))
    
    def test_save_job_data_json(self):
        """Test saving job data to JSON file."""
        output_folder = os.path.join(self.temp_dir, "test_output")
        os.makedirs(output_folder, exist_ok=True)
        
        json_path = self.service._save_job_data_json(self.project_data, output_folder)
        
        expected_path = os.path.join(output_folder, "TEST123 001.json")
        self.assertEqual(json_path, expected_path)
        self.assertTrue(os.path.exists(json_path))
        
        # Verify JSON content
        import json
        with open(json_path, 'r') as f:
            data = json.load(f)
        
        self.assertEqual(data['gss_parent'], "TEST123")
        self.assertEqual(data['serial_number'], "001")
        self.assertEqual(data['customer'], "Test Customer")
        self.assertIn('timestamp', data)
    
    def test_create_series_project_data(self):
        """Test creating project data for series item."""
        series_data = self.service._create_series_project_data(self.project_data, 2)
        
        self.assertEqual(series_data.gss_parent, self.project_data.gss_parent)
        self.assertEqual(series_data.customer, self.project_data.customer)
        self.assertEqual(series_data.serial_number, "003")  # 001 + 2
    
    def test_validate_publish_inputs_valid(self):
        """Test input validation with valid inputs."""
        # Should not raise exception
        self.service._validate_publish_inputs(self.project_data, self.publish_config)
    
    def test_validate_publish_inputs_missing_serial(self):
        """Test input validation with missing serial number."""
        invalid_data = MockProjectData()
        invalid_data.serial_number = ""
        
        with self.assertRaises(PublishError) as context:
            self.service._validate_publish_inputs(invalid_data, self.publish_config)
        
        self.assertIn("Serial number is required", str(context.exception))


class TestPublishConfig(unittest.TestCase):
    """Test cases for PublishConfig class."""
    
    def test_init_defaults(self):
        """Test PublishConfig initialization with defaults."""
        config = PublishConfig()
        
        self.assertEqual(config.output_base_path, "")
        self.assertEqual(config.export_formats, ['pdf', 'dxf'])
        self.assertFalse(config.create_manual)
        self.assertTrue(config.update_bom_reports)
        self.assertTrue(config.export_gss_bom)
        self.assertTrue(config.save_project)
        self.assertTrue(config.save_job_data)
        
        # Error handling defaults
        self.assertFalse(config.fail_on_e3_errors)
        self.assertFalse(config.fail_on_export_errors)
        self.assertFalse(config.fail_on_manual_errors)
        self.assertFalse(config.fail_on_report_errors)
        self.assertFalse(config.stop_series_on_error)
    
    def test_config_modification(self):
        """Test modifying PublishConfig settings."""
        config = PublishConfig()
        
        config.output_base_path = "/test/path"
        config.create_manual = True
        config.fail_on_e3_errors = True
        
        self.assertEqual(config.output_base_path, "/test/path")
        self.assertTrue(config.create_manual)
        self.assertTrue(config.fail_on_e3_errors)


class TestPublishResult(unittest.TestCase):
    """Test cases for PublishResult class."""
    
    def test_init_defaults(self):
        """Test PublishResult initialization with defaults."""
        result = PublishResult()
        
        self.assertFalse(result.success)
        self.assertEqual(result.serial_number, "")
        self.assertEqual(result.output_path, "")
        self.assertEqual(result.errors, [])
        self.assertEqual(result.warnings, [])
        
        # Detailed results
        self.assertEqual(result.steps_completed, [])
        self.assertEqual(result.exported_files, [])
        self.assertEqual(result.manual_files, [])
        self.assertFalse(result.manual_created)
        self.assertFalse(result.export_success)
        self.assertEqual(result.bom_reports_updated, 0)
        self.assertEqual(result.project_saved_path, "")
        self.assertEqual(result.gss_bom_path, "")
        self.assertEqual(result.job_data_path, "")
        self.assertIsNone(result.completed_at)
    
    def test_init_with_parameters(self):
        """Test PublishResult initialization with parameters."""
        errors = ["Error 1", "Error 2"]
        warnings = ["Warning 1"]
        
        result = PublishResult(
            success=True,
            serial_number="123",
            output_path="/test/path",
            errors=errors,
            warnings=warnings
        )
        
        self.assertTrue(result.success)
        self.assertEqual(result.serial_number, "123")
        self.assertEqual(result.output_path, "/test/path")
        self.assertEqual(result.errors, errors)
        self.assertEqual(result.warnings, warnings)
    
    def test_str_success(self):
        """Test string representation for successful result."""
        result = PublishResult(success=True, serial_number="123")
        result.steps_completed = ["step1", "step2", "step3"]
        
        str_repr = str(result)
        self.assertIn("SUCCESS", str_repr)
        self.assertIn("123", str_repr)
        self.assertIn("3 steps", str_repr)
    
    def test_str_failure(self):
        """Test string representation for failed result."""
        result = PublishResult(success=False, serial_number="456")
        result.steps_completed = ["step1"]
        
        str_repr = str(result)
        self.assertIn("FAILED", str_repr)
        self.assertIn("456", str_repr)
        self.assertIn("1 steps", str_repr)


class TestPublishError(unittest.TestCase):
    """Test cases for PublishError class."""
    
    def test_init_basic(self):
        """Test PublishError initialization with basic message."""
        error = PublishError("Test error")
        
        self.assertEqual(error.message, "Test error")
        self.assertIsNone(error.operation)
        self.assertIsNone(error.original_error)
    
    def test_init_with_operation(self):
        """Test PublishError initialization with operation."""
        error = PublishError("Test error", "publish_project")
        
        self.assertEqual(error.message, "Test error")
        self.assertEqual(error.operation, "publish_project")
        self.assertIsNone(error.original_error)
    
    def test_init_with_original_error(self):
        """Test PublishError initialization with original error."""
        original = ValueError("Original error")
        error = PublishError("Test error", "publish_project", original)
        
        self.assertEqual(error.message, "Test error")
        self.assertEqual(error.operation, "publish_project")
        self.assertEqual(error.original_error, original)
    
    def test_str_basic(self):
        """Test string representation with basic message."""
        error = PublishError("Test error")
        self.assertEqual(str(error), "Test error")
    
    def test_str_with_operation(self):
        """Test string representation with operation."""
        error = PublishError("Test error", "publish_project")
        expected = "Publish operation 'publish_project' failed: Test error"
        self.assertEqual(str(error), expected)
    
    def test_str_with_original_error(self):
        """Test string representation with original error."""
        original = ValueError("Original error")
        error = PublishError("Test error", "publish_project", original)
        expected = "Publish operation 'publish_project' failed: Test error (Original error: Original error)"
        self.assertEqual(str(error), expected)


if __name__ == '__main__':
    unittest.main()